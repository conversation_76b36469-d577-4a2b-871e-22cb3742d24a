# MedGemma AI Chat - Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the MedGemma AI Chat application.

## 🔍 General Debugging Steps

### 1. Check Service Status
```bash
# Check all services
docker-compose ps

# Check specific service logs
docker-compose logs -f medgemma-api
docker-compose logs -f nginx
docker-compose logs -f redis

# Check system resources
docker stats
free -h
df -h
```

### 2. Verify Configuration
```bash
# Check environment variables
cat .env

# Validate Docker Compose configuration
docker-compose config

# Check file permissions
ls -la
```

## 🚨 Common Issues and Solutions

### Issue 1: Model Download Fails

**Symptoms:**
- Service fails to start
- Logs show "Failed to download model"
- Authentication errors from HuggingFace

**Solutions:**

1. **Check HuggingFace Token:**
   ```bash
   # Verify token is set
   echo $HUGGINGFACE_TOKEN
   
   # Test token validity
   curl -H "Authorization: Bearer $HUGGINGFACE_TOKEN" \
        https://huggingface.co/api/whoami
   ```

2. **Accept Model License:**
   - Visit https://huggingface.co/google/medgemma-4b-it
   - Accept the license agreement
   - Ensure your account has access

3. **Manual Model Download:**
   ```bash
   # Download model manually
   docker-compose run --rm medgemma-api python scripts/download_model.py
   ```

### Issue 2: SSL Certificate Problems

**Symptoms:**
- Browser shows "Not Secure" warning
- SSL certificate errors in logs
- HTTPS not working

**Solutions:**

1. **Check Certificate Files:**
   ```bash
   # Check if certificates exist
   docker-compose exec nginx ls -la /etc/nginx/ssl/
   
   # Check certificate validity
   openssl x509 -in /path/to/cert.pem -text -noout
   ```

2. **Regenerate Let's Encrypt Certificate:**
   ```bash
   # Stop nginx
   docker-compose stop nginx
   
   # Generate new certificate
   docker-compose run --rm certbot certonly --standalone \
     --email <EMAIL> \
     --agree-tos \
     -d your-domain.com
   
   # Restart nginx
   docker-compose start nginx
   ```

3. **Use Self-Signed Certificate (Development):**
   ```bash
   # Generate self-signed certificate
   docker-compose exec nginx openssl req -x509 -nodes -days 365 \
     -newkey rsa:2048 \
     -keyout /etc/nginx/ssl/selfsigned.key \
     -out /etc/nginx/ssl/selfsigned.crt \
     -subj "/CN=localhost"
   ```

### Issue 3: Memory Issues

**Symptoms:**
- Services crash with OOM (Out of Memory) errors
- Slow response times
- System becomes unresponsive

**Solutions:**

1. **Monitor Memory Usage:**
   ```bash
   # Check memory usage
   free -h
   docker stats
   
   # Check swap usage
   swapon --show
   ```

2. **Add Swap Space:**
   ```bash
   # Create swap file (4GB)
   sudo fallocate -l 4G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   
   # Make permanent
   echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
   ```

3. **Optimize Docker Memory:**
   ```bash
   # Edit docker-compose.yml to limit memory
   # Add under medgemma-api service:
   deploy:
     resources:
       limits:
         memory: 12G
   ```

### Issue 4: API Authentication Errors

**Symptoms:**
- 401 Unauthorized errors
- "Invalid API key" messages
- Frontend can't connect to API

**Solutions:**

1. **Check API Key Configuration:**
   ```bash
   # Verify API key in environment
   grep API_KEY .env
   
   # Test API key
   curl -H "Authorization: Bearer your-api-key" \
        https://your-domain.com/api/health
   ```

2. **Update Frontend API Key:**
   ```javascript
   // In frontend/script.js, update getApiKey() method
   getApiKey() {
       return 'your-actual-api-key';
   }
   ```

### Issue 5: Image Upload Failures

**Symptoms:**
- Image uploads timeout
- "File too large" errors
- Image processing fails

**Solutions:**

1. **Check File Size Limits:**
   ```bash
   # Check nginx configuration
   docker-compose exec nginx grep client_max_body_size /etc/nginx/nginx.conf
   
   # Check application limits
   grep UPLOAD_MAX_SIZE .env
   ```

2. **Increase Upload Limits:**
   ```bash
   # Update .env
   UPLOAD_MAX_SIZE=20971520  # 20MB
   
   # Update nginx.conf
   client_max_body_size 20m;
   
   # Restart services
   docker-compose restart
   ```

3. **Test Image Upload:**
   ```bash
   # Test with curl
   curl -X POST https://your-domain.com/api/analyze-image \
     -H "Authorization: Bearer your-api-key" \
     -F "message=Test image" \
     -F "image=@test-image.jpg"
   ```

### Issue 6: Database Connection Issues

**Symptoms:**
- Redis connection errors
- Conversation history not saved
- Service startup failures

**Solutions:**

1. **Check Redis Status:**
   ```bash
   # Check Redis container
   docker-compose ps redis
   
   # Test Redis connection
   docker-compose exec redis redis-cli ping
   ```

2. **Reset Redis Data:**
   ```bash
   # Clear Redis data
   docker-compose exec redis redis-cli FLUSHALL
   
   # Restart Redis
   docker-compose restart redis
   ```

3. **Check Redis Configuration:**
   ```bash
   # Verify Redis URL
   grep REDIS_URL .env
   
   # Check Redis logs
   docker-compose logs redis
   ```

### Issue 7: Nginx Configuration Issues

**Symptoms:**
- 502 Bad Gateway errors
- Static files not loading
- Proxy errors

**Solutions:**

1. **Check Nginx Configuration:**
   ```bash
   # Test nginx configuration
   docker-compose exec nginx nginx -t
   
   # Reload nginx
   docker-compose exec nginx nginx -s reload
   ```

2. **Check Upstream Connection:**
   ```bash
   # Test backend connection
   docker-compose exec nginx curl http://medgemma-api:8000/health
   ```

3. **Debug Proxy Issues:**
   ```bash
   # Check nginx error logs
   docker-compose logs nginx | grep error
   
   # Check access logs
   docker-compose exec nginx tail -f /var/log/nginx/access.log
   ```

## 🔧 Performance Optimization

### 1. Model Loading Optimization

```bash
# Pre-download model to reduce startup time
docker-compose run --rm medgemma-api python scripts/download_model.py

# Use model caching
# Ensure MODEL_CACHE_DIR is properly mounted
```

### 2. Memory Optimization

```bash
# Enable memory monitoring
docker-compose exec medgemma-api pip install psutil

# Monitor memory usage
docker-compose exec medgemma-api python -c "
import psutil
print(f'Memory: {psutil.virtual_memory().percent}%')
print(f'CPU: {psutil.cpu_percent()}%')
"
```

### 3. Network Optimization

```bash
# Enable gzip compression in nginx
# Already configured in nginx.conf

# Use HTTP/2
# Already configured for HTTPS
```

## 📊 Monitoring and Alerting

### 1. Set Up Health Monitoring

```bash
# Create health check script
cat > health_check.sh << 'EOF'
#!/bin/bash
HEALTH_URL="https://your-domain.com/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
EOF

chmod +x health_check.sh

# Add to crontab for monitoring
crontab -e
# Add: */5 * * * * /path/to/health_check.sh
```

### 2. Log Monitoring

```bash
# Monitor error logs
tail -f /var/log/nginx/error.log | grep -i error

# Monitor application logs
docker-compose logs -f medgemma-api | grep -i error
```

## 🆘 Emergency Recovery

### 1. Complete Service Restart

```bash
# Stop all services
docker-compose down

# Clean up
docker system prune -f

# Restart services
docker-compose up -d
```

### 2. Restore from Backup

```bash
# Stop services
docker-compose down

# Restore configuration
tar -xzf backup/medgemma_config_YYYYMMDD.tar.gz

# Restore Redis data
docker-compose up -d redis
docker cp backup/redis_YYYYMMDD.rdb medgemma-redis:/data/dump.rdb
docker-compose restart redis

# Start all services
docker-compose up -d
```

### 3. Factory Reset

```bash
# Complete cleanup
docker-compose down -v
docker system prune -a -f

# Remove all data
sudo rm -rf model_cache/ uploads/ logs/

# Redeploy from scratch
docker-compose up -d
```

## 📞 Getting Help

### 1. Collect Debug Information

```bash
# Create debug report
cat > debug_report.txt << EOF
=== System Information ===
$(uname -a)
$(free -h)
$(df -h)

=== Docker Information ===
$(docker --version)
$(docker-compose --version)
$(docker-compose ps)

=== Service Logs ===
$(docker-compose logs --tail=50 medgemma-api)

=== Configuration ===
$(docker-compose config)
EOF
```

### 2. Contact Support

- GitHub Issues: Create detailed issue with debug report
- Include error logs and configuration
- Specify your environment (AWS, local, etc.)
- Provide steps to reproduce the issue

### 3. Community Resources

- Documentation: Check all docs/ files
- Examples: Review working configurations
- Forums: Search for similar issues online
