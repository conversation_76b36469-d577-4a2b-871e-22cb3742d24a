# Development Nginx Dockerfile (HTTP only, no SSL)
FROM nginx:alpine

# Copy development nginx configuration
COPY nginx/nginx.dev.conf /etc/nginx/nginx.conf
COPY nginx/conf.d/medgemma.dev.conf /etc/nginx/conf.d/default.conf

# Copy frontend files
COPY frontend/ /usr/share/nginx/html/

# Create nginx user and set permissions
RUN addgroup -g 101 -S nginx && \
    adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Set proper permissions
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Expose only HTTP port for development
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
