#!/bin/bash

# MedGemma AI Chat Development Deployment Script
# This script sets up the application for development with dynamic IP addresses

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root. Please run as ubuntu user."
fi

# Configuration
HUGGINGFACE_TOKEN=""
API_KEY=""
REDIS_PASSWORD=""

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Function to get current public IP
get_public_ip() {
    # Try multiple services to get public IP
    PUBLIC_IP=$(curl -s http://checkip.amazonaws.com/ 2>/dev/null || \
                curl -s http://ipv4.icanhazip.com/ 2>/dev/null || \
                curl -s http://ifconfig.me/ip 2>/dev/null || \
                echo "Unable to determine public IP")
    echo "$PUBLIC_IP"
}

# Function to collect configuration
collect_config() {
    log "Collecting development configuration..."
    
    # HuggingFace token
    while true; do
        read -p "Enter HuggingFace token (required for MedGemma): " HUGGINGFACE_TOKEN
        if [[ -n "$HUGGINGFACE_TOKEN" ]]; then
            break
        else
            warn "HuggingFace token is required. Get it from https://huggingface.co/settings/tokens"
            warn "Make sure you've accepted the MedGemma license at https://huggingface.co/google/medgemma-4b-it"
        fi
    done
    
    # API key
    read -p "Enter API key for development (leave empty to use default): " API_KEY
    if [[ -z "$API_KEY" ]]; then
        API_KEY="dev-api-key-for-testing-only"
        info "Using default development API key: $API_KEY"
    fi
    
    # Redis password
    read -p "Enter Redis password (leave empty to generate): " REDIS_PASSWORD
    if [[ -z "$REDIS_PASSWORD" ]]; then
        REDIS_PASSWORD="dev-redis-password"
        info "Using default development Redis password: $REDIS_PASSWORD"
    fi
    
    log "Configuration collected successfully!"
}

# Function to check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check if we're on EC2
    if curl -s --max-time 3 http://169.254.169.254/latest/meta-data/instance-id >/dev/null 2>&1; then
        info "Running on AWS EC2 instance"
        INSTANCE_ID=$(curl -s http://169.254.169.254/latest/meta-data/instance-id)
        INSTANCE_TYPE=$(curl -s http://169.254.169.254/latest/meta-data/instance-type)
        info "Instance ID: $INSTANCE_ID"
        info "Instance Type: $INSTANCE_TYPE"
    else
        warn "Not running on AWS EC2 or metadata service unavailable"
    fi
    
    # Check memory
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 15 ]]; then
        warn "Recommended memory is 16GB, you have ${MEMORY_GB}GB"
    else
        info "Memory: ${MEMORY_GB}GB (sufficient)"
    fi
    
    # Check disk space
    DISK_GB=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_GB -lt 40 ]]; then
        warn "Recommended free disk space is 50GB, you have ${DISK_GB}GB"
    else
        info "Disk space: ${DISK_GB}GB available (sufficient)"
    fi
    
    log "System requirements check completed"
}

# Function to update system
update_system() {
    log "Updating system packages..."
    sudo apt update
    sudo apt upgrade -y
    sudo apt install -y curl wget git unzip software-properties-common bc
    log "System updated successfully"
}

# Function to install Docker
install_docker() {
    log "Installing Docker..."
    
    if command -v docker &> /dev/null; then
        info "Docker is already installed"
        docker --version
        return
    fi
    
    # Install Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    rm get-docker.sh
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log "Docker installed successfully"
    info "You may need to log out and back in for Docker group changes to take effect"
}

# Function to install Docker Compose
install_docker_compose() {
    log "Installing Docker Compose..."
    
    if command -v docker-compose &> /dev/null; then
        info "Docker Compose is already installed"
        docker-compose --version
        return
    fi
    
    # Install Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # Verify installation
    docker-compose --version
    
    log "Docker Compose installed successfully"
}

# Function to create development environment file
create_dev_env_file() {
    log "Creating development environment configuration..."
    
    # Copy development template
    cp .env.development .env
    
    # Update with user-provided values
    sed -i "s/HUGGINGFACE_TOKEN=your-huggingface-token-here/HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN/" .env
    sed -i "s/HF_TOKEN=your-huggingface-token-here/HF_TOKEN=$HUGGINGFACE_TOKEN/" .env
    sed -i "s/API_KEY=dev-api-key-for-testing-only/API_KEY=$API_KEY/" .env
    sed -i "s/REDIS_PASSWORD=dev-redis-password/REDIS_PASSWORD=$REDIS_PASSWORD/" .env
    
    log "Development environment file created successfully"
}

# Function to build and start services
deploy_services() {
    log "Building and deploying development services..."
    
    # Build services using development compose file
    docker-compose -f docker-compose.dev.yml build
    
    # Start services
    docker-compose -f docker-compose.dev.yml up -d
    
    log "Development services deployed successfully"
}

# Function to verify deployment
verify_deployment() {
    log "Verifying development deployment..."
    
    # Wait for services to start
    info "Waiting for services to start (this may take a few minutes for model download)..."
    sleep 30
    
    # Check service status
    docker-compose -f docker-compose.dev.yml ps
    
    # Get public IP
    PUBLIC_IP=$(get_public_ip)
    
    # Test health endpoint
    info "Testing health endpoint..."
    for i in {1..10}; do
        if curl -f -s "http://localhost/api/health" >/dev/null 2>&1; then
            log "Health check passed"
            break
        elif [[ $i -eq 10 ]]; then
            warn "Health check failed after 10 attempts - services may still be starting"
            warn "Check logs with: docker-compose -f docker-compose.dev.yml logs -f"
        else
            info "Attempt $i/10: Waiting for services to be ready..."
            sleep 30
        fi
    done
    
    log "Development deployment verification completed"
}

# Function to display completion message
show_completion() {
    PUBLIC_IP=$(get_public_ip)
    
    log "Development deployment completed successfully!"
    echo
    echo -e "${BLUE}=== Development Deployment Summary ===${NC}"
    echo -e "Environment: Development (HTTP only)"
    echo -e "Public IP: $PUBLIC_IP"
    echo -e "Application URL: http://$PUBLIC_IP"
    echo -e "API Base URL: http://$PUBLIC_IP/api"
    echo -e "API Key: $API_KEY"
    echo -e "Redis Password: $REDIS_PASSWORD"
    echo
    echo -e "${BLUE}=== Access Your Application ===${NC}"
    echo "1. Open your browser and go to: http://$PUBLIC_IP"
    echo "2. The chat interface should load automatically"
    echo "3. Start chatting with the AI or upload medical images"
    echo
    echo -e "${BLUE}=== Testing the API ===${NC}"
    echo "Test the health endpoint:"
    echo "  curl http://$PUBLIC_IP/api/health"
    echo
    echo "Test a chat message:"
    echo "  curl -X POST http://$PUBLIC_IP/api/chat \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -H 'Authorization: Bearer $API_KEY' \\"
    echo "    -d '{\"message\": \"Hello, how are you?\"}'"
    echo
    echo -e "${BLUE}=== Monitoring ===${NC}"
    echo "View logs: docker-compose -f docker-compose.dev.yml logs -f"
    echo "Check status: docker-compose -f docker-compose.dev.yml ps"
    echo "Prometheus metrics: http://$PUBLIC_IP:9090"
    echo
    echo -e "${BLUE}=== Important Notes ===${NC}"
    echo "- This is a DEVELOPMENT setup using HTTP (not HTTPS)"
    echo "- The API key is for development only"
    echo "- Your EC2 public IP will change if you restart the instance"
    echo "- To move to production, use the production deployment guide"
    echo
    echo -e "${BLUE}=== Next Steps ===${NC}"
    echo "1. Test all functionality thoroughly"
    echo "2. When ready for production, follow docs/DEPLOYMENT.md"
    echo "3. For issues, check docs/TROUBLESHOOTING.md"
    echo
}

# Function to setup firewall for development
setup_dev_firewall() {
    log "Setting up development firewall..."
    
    # Install and configure UFW
    sudo apt install -y ufw
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    sudo ufw allow ssh
    sudo ufw allow 80/tcp
    sudo ufw allow 9090/tcp  # Prometheus
    sudo ufw --force enable
    
    log "Development firewall configured successfully"
}

# Main deployment function
main() {
    log "Starting MedGemma AI Chat development deployment..."
    
    # Check if .env already exists
    if [[ -f .env ]]; then
        read -p "Configuration file (.env) already exists. Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Using existing configuration"
        else
            collect_config
            create_dev_env_file
        fi
    else
        collect_config
        create_dev_env_file
    fi
    
    check_requirements
    update_system
    install_docker
    install_docker_compose
    setup_dev_firewall
    deploy_services
    verify_deployment
    show_completion
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
